{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Neural Chef Assistant - FIT5217 Assignment 2\n",
    "\n",
    "**Student ID**: 01234567\n",
    "**Date**: July 2025\n",
    "\n",
    "## Project Overview\n",
    "This project implements a Neural Chef Assistant that generates cooking recipes from ingredient lists using deep neural networks.\n",
    "\n",
    "### Models Implemented:\n",
    "1. **Baseline 1**: Sequence-to-sequence without attention (4 marks)\n",
    "2. **Baseline 2**: Sequence-to-sequence with attention (5 marks)\n",
    "3. **Light Extension 1**: Bidirectional LSTM encoder (8 marks)\n",
    "4. **Light Extension 2**: Multi-layer decoder with dropout (8 marks)\n",
    "5. **Hard Extension 1**: Copy mechanism (10 marks)\n",
    "6. **Hard Extension 2**: Coverage mechanism (10 marks)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Import Libraries and Setup"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import torch\n",
    "import torch.nn as nn\n",
    "import torch.optim as optim\n",
    "import torch.nn.functional as F\n",
    "from torch.utils.data import Dataset, DataLoader\n",
    "from torch.nn.utils.rnn import pad_sequence, pack_padded_sequence, pad_packed_sequence\n",
    "\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import json\n",
    "import re\n",
    "import random\n",
    "import pickle\n",
    "from collections import Counter, defaultdict\n",
    "from tqdm.notebook import tqdm\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "\n",
    "# For evaluation metrics\n",
    "import nltk\n",
    "from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction\n",
    "from nltk.translate.meteor_score import meteor_score\n",
    "from bert_score import score\n",
    "\n",
    "# Download required NLTK data\n",
    "try:\n",
    "    nltk.download('punkt_tab')\n",
    "    nltk.download('wordnet')\n",
    "    nltk.download('omw-1.4')\n",
    "except:\n",
    "    pass\n",
    "\n",
    "# Set random seeds for reproducibility\n",
    "torch.manual_seed(42)\n",
    "np.random.seed(42)\n",
    "random.seed(42)\n",
    "\n",
    "# Device configuration\n",
    "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n",
    "print(f'Using device: {device}')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Data Loading and Preprocessing"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def load_data():\n",
    "    \"\"\"Load the cooking dataset from CSV files\"\"\"\n",
    "    train_df = pd.read_csv('Cooking_Dataset/Cooking_Dataset/train.csv')\n",
    "    dev_df = pd.read_csv('Cooking_Dataset/Cooking_Dataset/dev.csv')\n",
    "    test_df = pd.read_csv('Cooking_Dataset/Cooking_Dataset/test.csv')\n",
    "    \n",
    "    print(f\"Train set size: {len(train_df)}\")\n",
    "    print(f\"Dev set size: {len(dev_df)}\")\n",
    "    print(f\"Test set size: {len(test_df)}\")\n",
    "    \n",
    "    return train_df, dev_df, test_df\n",
    "\n",
    "# Load data\n",
    "train_df, dev_df, test_df = load_data()\n",
    "\n",
    "# Display sample data\n",
    "print(\"\\nSample training data:\")\n",
    "print(train_df.head(2))"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def preprocess_text(text):\n",
    "    \"\"\"Clean and preprocess text data\"\"\"\n",
    "    if pd.isna(text):\n",
    "        return []\n",
    "    \n",
    "    # Parse JSON-like string format\n",
    "    try:\n",
    "        text = text.strip()\n",
    "        if text.startswith('[') and text.endswith(']'):\n",
    "            items = json.loads(text)\n",
    "        else:\n",
    "            items = [text]\n",
    "    except:\n",
    "        items = [text]\n",
    "    \n",
    "    processed_items = []\n",
    "    for item in items:\n",
    "        # Clean text: lowercase, remove extra spaces, basic punctuation\n",
    "        item = str(item).lower().strip()\n",
    "        item = re.sub(r'[^a-zA-Z0-9\\s.,()/-]', '', item)\n",
    "        item = re.sub(r'\\s+', ' ', item)\n",
    "        if item:\n",
    "            processed_items.append(item)\n",
    "    \n",
    "    return processed_items\n",
    "\n",
    "def tokenize_text(text_list):\n",
    "    \"\"\"Tokenize a list of text items\"\"\"\n",
    "    tokens = []\n",
    "    for text in text_list:\n",
    "        words = text.split()\n",
    "        tokens.extend(words)\n",
    "    return tokens"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Vocabulary Building"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class Vocabulary:\n",
    "    def __init__(self, min_freq=2):\n",
    "        self.min_freq = min_freq\n",
    "        self.word2idx = {}\n",
    "        self.idx2word = {}\n",
    "        self.word_freq = Counter()\n",
    "        \n",
    "        # Special tokens\n",
    "        self.PAD_TOKEN = '<PAD>'\n",
    "        self.UNK_TOKEN = '<UNK>'\n",
    "        self.SOS_TOKEN = '
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
